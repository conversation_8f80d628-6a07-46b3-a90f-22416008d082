# Environment Variables Configuration

## Overview

This document lists all environment variables used by the SmartHR application and their expected formats.

## Critical URL Configuration

**IMPORTANT**: All URL environment variables must include proper HTTP/HTTPS schemes to avoid CORS and network errors.

### API URLs

| Variable | Description | Example | Required Scheme |
|----------|-------------|---------|-----------------|
| `LUMUS_API_URL` | Lumus API endpoint for file processing | `http://localhost:8000` | http/https |
| `API_CALLBACK_URL` | Callback URL for async operations | `http://localhost:8080` | http/https |
| `AZURE_OPENAI_ENDPOINT` | Azure OpenAI service endpoint | `https://your-resource.openai.azure.com` | https |

### Database URLs

| Variable | Description | Example |
|----------|-------------|---------|
| `POSTGRES_HOST` | PostgreSQL host | `localhost` |
| `POSTGRES_PORT` | PostgreSQL port | `5432` |
| `POSTGRES_USER` | PostgreSQL username | `postgres` |
| `POSTGRES_PASSWORD` | PostgreSQL password | `your_password` |
| `POSTGRES_DB` | PostgreSQL database name | `smarthr` |

### Authentication

| Variable | Description | Example |
|----------|-------------|---------|
| `TENANT_ID` | Azure AD tenant ID | `19eee545-4131-45c6-9a60-1a17e5cc507d` |
| `CLIENT_ID_SMART_HR` | SmartHR app client ID | `d35b6143-5032-41ba-b03b-1dae6c2cda10` |
| `AZURE_OPENAI_API_KEY` | Azure OpenAI API key | `your_api_key` |

### LinkedIn Integration

| Variable | Description | Example |
|----------|-------------|---------|
| `LINKEDIN_API_PROVIDER` | LinkedIn API provider | `mock_provider` |
| `LINKEDIN_CLIENT_ID` | LinkedIn app client ID | `your_client_id` |
| `LINKEDIN_CLIENT_SECRET` | LinkedIn app client secret | `your_client_secret` |

## Common Issues

### URL Scheme Errors

**Error**: `Failed to fetch. URL scheme must be "http" or "https" for CORS request.`

**Cause**: Environment variables containing URLs without proper schemes (e.g., `localhost` instead of `http://localhost`)

**Solution**: Ensure all URL variables include the protocol:
- ✅ `LUMUS_API_URL=http://localhost:8000`
- ❌ `LUMUS_API_URL=localhost:8000`

### CORS Issues

**Error**: `CORS policy: No 'Access-Control-Allow-Origin' header`

**Cause**: Backend CORS configuration or incorrect URL schemes

**Solution**: 
1. Verify URL schemes are correct
2. Check CORS middleware configuration in `main.py`
3. Ensure frontend is making requests to the correct backend URL

## Validation

The application includes URL validation for critical endpoints. Check the logs for warnings about missing or invalid URL schemes.

## Development vs Production

### Development
```bash
LUMUS_API_URL=http://localhost:8000
API_CALLBACK_URL=http://localhost:8080
```

### Production
```bash
LUMUS_API_URL=https://your-lumus-api.com
API_CALLBACK_URL=https://your-smarthr-api.com
```
